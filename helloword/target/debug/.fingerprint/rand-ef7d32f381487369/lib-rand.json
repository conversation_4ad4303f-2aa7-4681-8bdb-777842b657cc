{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2241668132362809309, "path": 2581055115701760288, "deps": [[1573238666360410412, "rand_chacha", false, 1120254356748511191], [4684437522915235464, "libc", false, 11019619854410679743], [18130209639506977569, "rand_core", false, 8789979015448436360]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rand-ef7d32f381487369/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}