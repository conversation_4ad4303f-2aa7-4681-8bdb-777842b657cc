{"rustc": 11410426090777951712, "features": "[\"clone-impls\", \"default\", \"derive\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 9546857608814333965, "deps": [[1988483478007900009, "unicode_ident", false, 11441378176739679562], [3060637413840920116, "proc_macro2", false, 13890419900855597153], [17990358020177143287, "quote", false, 16420067029517163418]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-ce7bdc502530fede/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}