# Rust build artifacts
/target/
**/*.rs.bk
Cargo.lock

# Python artifacts
__pycache__/
*.py[cod]
*$py.class
*.so
*.pyd
*.dll
*.dylib
*.egg-info/
dist/
build/
.eggs/

# Virtual environments
kunquant-env/
venv/
env/
.env

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test artifacts
/tests/target/
/examples/target/

# Documentation build
/target/doc/

# Temporary files
*.tmp
*.temp
