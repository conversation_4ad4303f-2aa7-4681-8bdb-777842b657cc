#include "Table.hpp"
#include <Kun/Base.hpp>
#include <stdint.h>

namespace kun_simd {

// generated by gen_table.py
template <>
alignas(64) const float LogLookupTable<float>::r_table[32] = {
    1.0f,    0.96875f, 0.9375f,  0.90625f, 0.875f, 0.84375f, 0.84375f,
    0.8125f, 0.78125f, 0.78125f, 0.75f,    0.75f,  0.71875f, 0.71875f,
    0.6875f, 0.6875f,  1.3125f,  1.3125f,  1.25f,  1.25f,    1.25f,
    1.1875f, 1.1875f,  1.125f,   1.125f,   1.125f, 1.125f,   1.0625f,
    1.0625f, 1.0625f,  1.0f,     1.0f};
template <>
alignas(64) const float LogLookupTable<float>::logr_table[32] = {
    -88.02969193111305f, -87.99794323279846f, -87.96515340997547f,
    -87.9312518582998f,  -87.89616053848853f, -87.85979289431765f,
    -87.85979289431765f, -87.8220525663348f,  -87.78283185318152f,
    -87.78283185318152f, -87.74200985866126f, -87.74200985866126f,
    -87.69945024424247f, -87.69945024424247f, -87.65499848167164f,
    -87.65499848167164f, -88.30162564659669f, -88.30162564659669f,
    -88.25283548242726f, -88.25283548242726f, -88.25283548242726f,
    -88.20154218803971f, -88.20154218803971f, -88.14747496676944f,
    -88.14747496676944f, -88.14747496676944f, -88.14747496676944f,
    -88.09031655292948f, -88.09031655292948f, -88.09031655292948f,
    -88.02969193111305f, -88.02969193111305f};

template <>
alignas(64) const double LogLookupTable<double>::r_table[32] = {
    1.0,     0.96875, 0.9375, 0.90625, 0.875,   0.84375, 0.84375, 0.8125,
    0.78125, 0.78125, 0.75,   0.75,    0.71875, 0.71875, 0.6875,  0.6875,
    1.3125,  1.3125,  1.25,   1.25,    1.25,    1.1875,  1.1875,  1.125,
    1.125,   1.125,   1.125,  1.0625,  1.0625,  1.0625,  1.0,     1.0};
template <>
alignas(64) const double LogLookupTable<double>::logr_table[32] = {
    -709.0895657128241, -709.0578170145095, -709.0250271916865,
    -708.9911256400109, -708.9560343201996, -708.9196666760287,
    -708.9196666760287, -708.8819263480458, -708.8427056348926,
    -708.8427056348926, -708.8018836403724, -708.8018836403724,
    -708.7593240259536, -708.7593240259536, -708.7148722633826,
    -708.7148722633826, -709.3614994283078, -709.3614994283078,
    -709.3127092641383, -709.3127092641383, -709.3127092641383,
    -709.2614159697507, -709.2614159697507, -709.2073487484805,
    -709.2073487484805, -709.2073487484805, -709.2073487484805,
    -709.1501903346406, -709.1501903346406, -709.1501903346406,
    -709.0895657128241, -709.0895657128241};
} // namespace kun_simd