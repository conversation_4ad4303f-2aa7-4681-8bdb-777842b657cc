# KunQuant-rs

Rust bindings for the KunQuant financial factor computation library.

## Overview

KunQuant-rs provides safe Rust bindings for KunQuant, a high-performance financial factor computation library. This crate wraps the C API with safe Rust abstractions while maintaining the performance characteristics of the underlying library.

## Features

- **Safe Rust API**: All unsafe FFI calls are wrapped in safe abstractions
- **RAII Resource Management**: Automatic cleanup of resources using Rust's Drop trait
- **Batch Computation**: Efficient batch processing of financial factors
- **Multi-threading Support**: Both single-thread and multi-thread executors
- **Streaming Computation**: Real-time factor calculation with low latency
- **Memory Safety**: Proper lifetime management and buffer handling

## Quick Start

### Prerequisites

1. Install KunQuant Python package in a virtual environment:
```bash
uv venv kunquant-env
source kunquant-env/bin/activate  # On Windows: kunquant-env\Scripts\activate
uv pip install ./KunQuant
```

2. Generate test factor libraries:
```bash
python generate_test_factor.py
```

### Basic Usage

```rust
use kunquant_rs::{Executor, Library, BufferNameMap, BatchParams, run_graph};

// Create executor and load library
let executor = Executor::single_thread()?;
let library = Library::load("test_libs/simple_test_lib.so")?;
let module = library.get_module("simple_test")?;

// Prepare data
let mut input_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
let mut output_data = vec![0.0; 8];

// Set up buffers
let mut buffers = BufferNameMap::new()?;
buffers.set_buffer_slice("input", &mut input_data)?;
buffers.set_buffer_slice("output", &mut output_data)?;

// Run computation
let params = BatchParams::full_range(8, 1)?;
run_graph(&executor, &module, &buffers, &params)?;

// Results are now in output_data
println!("Results: {:?}", output_data);
```

## API Documentation

### Core Types

- `Executor`: Manages computation execution (single-thread or multi-thread)
- `Library`: Represents a loaded factor library
- `Module`: A specific factor module within a library
- `BufferNameMap`: Maps buffer names to data slices
- `BatchParams`: Parameters for batch computation
- `StreamContext`: Context for streaming computation (experimental)

### Key Functions

- `run_graph()`: Execute a factor computation graph
- `Executor::single_thread()`: Create single-thread executor
- `Executor::multi_thread(n)`: Create multi-thread executor with n threads
- `Library::load(path)`: Load a factor library from file

## Testing

Run tests with the provided script that sets up the correct library path:

```bash
./run_tests.sh
```

### Test Results

✅ **Batch Computation Tests**
- Simple factor batch processing: PASSED
- Multi-thread executor: PASSED
- Alpha001 factor computation: PASSED
- Partial range computation: PASSED

✅ **Streaming Tests**
- Streaming computation: PASSED
- Real-time factor calculation: PASSED
- Buffer management: PASSED

## Examples

### Simple Factor Test
The simple test factor computes: `output = input * 3`

### Alpha001 Factor Test
Implements the Alpha001 factor from the Alpha101 library, which involves:
- Price return calculations
- Rolling standard deviation (20-day window)
- Time-series argmax (5-day window)
- Cross-sectional ranking

## Architecture

The library follows a layered architecture:

1. **FFI Layer** (`ffi.rs`): Raw C bindings
2. **Error Handling** (`error.rs`): Rust error types
3. **Core Types** (`executor.rs`, `library.rs`): Safe wrappers
4. **Buffer Management** (`buffer.rs`): Memory-safe buffer handling
5. **Computation APIs** (`batch.rs`, `stream.rs`): High-level computation interfaces

## Memory Management

- All C resources are automatically cleaned up using Rust's RAII pattern
- Buffer lifetimes are tracked to prevent use-after-free
- No manual memory management required

## Performance

- Zero-cost abstractions over the C API
- Efficient buffer management with minimal copying
- Multi-threading support for parallel computation
- Stock count must be a multiple of 8 for optimal SIMD performance

## Limitations

- Requires KunQuant C library to be installed and accessible
- Factor libraries must be pre-compiled using the Python interface
- Streaming factors require special compilation with `output_layout="STREAM"`

## Contributing

1. Ensure all tests pass: `./run_tests.sh`
2. Add tests for new functionality
3. Follow Rust naming conventions and safety guidelines
4. Document public APIs

## License

MIT License - see LICENSE file for details.
