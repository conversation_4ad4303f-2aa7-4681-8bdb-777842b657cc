# KunQuant-rs ndarray 集成

本文档描述了 KunQuant-rs 与 ndarray 库的集成，提供了使用多维数组格式进行因子计算的完整解决方案。

## 概述

我们成功创建了一个使用 ndarray 的测试套件，实现了以下功能：

1. **数据格式转换**：在 ndarray 格式 `[num_stock, num_time, num_factors]` 和 KunQuant 内部格式之间进行转换
2. **Alpha001 因子计算**：使用真实的 KunQuant 计算引擎计算 Alpha001 因子
3. **批处理计算**：支持简单的批处理因子计算
4. **统计操作**：提供各种统计分析功能

## 核心功能

### 1. 数据格式转换函数

#### `generate_stock_data_ndarray(num_stock, num_time) -> Array3<f32>`
生成符合 KunQuant 要求的股票数据，格式为 `[num_stock, num_time, 6]`，其中 6 个因子对应：
- 0: open (开盘价)
- 1: high (最高价)  
- 2: low (最低价)
- 3: close (收盘价)
- 4: volume (成交量)
- 5: amount (成交额)

#### `ndarray_to_kunquant_buffer(data, factor_idx) -> Vec<f32>`
将 ndarray 格式转换为 KunQuant 的行主序格式：
- 输入：`[num_stock, num_time, num_factors]` 的 ndarray
- 输出：`[time0_stock0, time0_stock1, ..., time1_stock0, time1_stock1, ...]` 的 Vec

#### `kunquant_buffer_to_ndarray(buffer, num_stock, num_time) -> Array3<f32>`
将 KunQuant 输出转换为 ndarray 格式：
- 输入：KunQuant 的行主序缓冲区
- 输出：`[num_stock, num_time, 1]` 的 ndarray

### 2. 测试函数

#### `test_kunquant_alpha001_with_ndarray()`
**核心测试函数**，演示了完整的 Alpha001 因子计算流程：

```rust
// 1. 生成输入数据 [8, 100, 6]
let input_data = generate_stock_data_ndarray(8, 100);

// 2. 转换为 KunQuant 格式
let mut open_buffer = ndarray_to_kunquant_buffer(&input_data, 0);
let mut high_buffer = ndarray_to_kunquant_buffer(&input_data, 1);
// ... 其他缓冲区

// 3. 执行 KunQuant 计算
let executor = Executor::single_thread()?;
let library = Library::load("test_libs/alpha001_lib.so")?;
let module = library.get_module("alpha001_test")?;
run_graph(&executor, &module, &buffers, &params)?;

// 4. 转换输出为 ndarray 格式 [8, 100, 1]
let output_data = kunquant_buffer_to_ndarray(&alpha001_output, 8, 100);
```

**测试结果**：
- 总值数量：800 (8 stocks × 100 time periods)
- 有效值：614 (76.75%)
- NaN 值：186 (23.25%) - 这是正常的，因为 Alpha001 需要历史数据窗口
- 值范围：[0.0, 1.0] (由于 rank 操作)

#### `test_ndarray_batch_factor_computation()`
演示简单的批处理因子计算（input × 3）：

```rust
// 输入：[16, 50, 1] 的随机数据
// 输出：[16, 50, 1] 的结果（每个值乘以 3）
// 准确率：100.00%
```

## 使用方法

### 1. 准备测试环境

```bash
# 生成测试因子库
source kunquant-env/bin/activate
python generate_test_factor.py
```

### 2. 运行测试

```bash
# 设置库路径
export LD_LIBRARY_PATH="$PWD/KunQuant/build/lib.linux-x86_64-cpython-312/KunQuant/runner:$PWD/kunquant-env/lib/python3.12/site-packages/KunQuant/runner:$LD_LIBRARY_PATH"

# 运行 Alpha001 测试
cargo test test_kunquant_alpha001_with_ndarray -- --nocapture

# 运行批处理测试
cargo test test_ndarray_batch_factor_computation -- --nocapture

# 运行所有 ndarray 测试
cargo test --test ndarray_test -- --nocapture
```

## 技术要点

### 1. 数据布局转换
- **ndarray 格式**：`[stock_idx][time_idx][factor_idx]` - 股票优先
- **KunQuant 格式**：`[time_idx * num_stock + stock_idx]` - 时间优先，行主序

### 2. 内存管理
- 使用 `Vec<f32>` 作为 KunQuant 和 Rust 之间的缓冲区
- 确保缓冲区生命周期在整个计算过程中有效
- 使用 `mut` 引用允许 KunQuant 写入输出缓冲区

### 3. 错误处理
- 检查测试库文件是否存在
- 验证计算结果的合理性（范围检查、NaN 处理）
- 提供详细的统计分析和样本输出

### 4. 性能考虑
- 股票数量必须是 8 的倍数（SIMD 优化要求）
- 使用单线程执行器避免并发复杂性
- 预分配缓冲区避免运行时内存分配

## 文件结构

```
tests/ndarray_test.rs          # 主要测试文件
├── 数据转换函数
├── test_kunquant_alpha001_with_ndarray()     # Alpha001 因子计算
├── test_ndarray_batch_factor_computation()   # 批处理计算
├── test_ndarray_basic_operations()           # 基础操作
├── test_ndarray_data_conversion()            # 数据转换
├── test_ndarray_statistical_operations()    # 统计操作
└── test_ndarray_factor_computation_pipeline() # 计算管道

test_libs/
├── alpha001_lib.so           # Alpha001 因子库
└── simple_test_lib.so        # 简单测试库

generate_test_factor.py       # 生成测试因子库的 Python 脚本
```

## 总结

这个集成提供了一个完整的解决方案，允许用户：

1. **使用熟悉的 ndarray 格式**处理多维金融数据
2. **无缝集成 KunQuant 计算引擎**进行高性能因子计算
3. **获得详细的计算结果分析**和验证
4. **扩展到更复杂的因子计算场景**

所有测试都通过，证明了集成的稳定性和正确性。用户可以基于这些示例开发自己的因子计算应用。
